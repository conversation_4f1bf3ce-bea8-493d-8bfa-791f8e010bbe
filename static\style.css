/* Enhanced CSS with all styling improvements */
:root {
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #818cf8;
  --secondary: #06b6d4;
  --secondary-dark: #0891b2;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  
  --bg: #0f172a;
  --bg-light: #1e293b;
  --surface: #1e293b;
  --surface-light: #334155;
  --surface-lighter: #475569;
  
  --muted: #6b7280;
  --accent: #6366f1;
  --accent-2: #06b6d4;
  --glass: rgba(255,255,255,0.06);
  --shadow: 0 6px 24px rgba(12,20,30,0.08);
  --radius: 12px;
  --max-width: 1200px;
  --gap: 18px;
  --tile-bg: #0b1020;
}

/* --- base layout --- */
* { box-sizing: border-box; }
html,body { height:100%; margin:0; font-family: Inter,ui-sans-serif,system-ui,-apple-system,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",Arial; background: linear-gradient(180deg,#071020 0%, #0f1724 100%); color: #e6eef8; -webkit-font-smoothing:antialiased; -moz-osx-font-smoothing:grayscale; }
a { color:var(--accent); text-decoration:none; }
.app { max-width: var(--max-width); margin:28px auto; padding:20px; }

/* --- topbar --- */
.topbar { display:flex; align-items:center; justify-content:space-between; gap:20px; padding:14px; border-radius:14px; background:linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01)); box-shadow: var(--shadow); }
.brand { display:flex; gap:12px; align-items:center; }
.logo { width:44px; height:44px; color: var(--accent-2); }
.title { font-weight:700; font-size:16px; color:#fff; }
.subtitle { font-size:12px; color:var(--muted); margin-top:2px; }

/* --- actions --- */
.actions { display:flex; gap:10px; align-items:center; }
.room-input { padding:10px 12px; border-radius:10px; border:1px solid rgba(255,255,255,0.04); background:transparent; color:inherit; width:220px; outline:none; }
.room-input::placeholder { color: rgba(230,238,248,0.45); }

.btn { padding:10px 14px; border-radius:10px; border:0; cursor:pointer; font-weight:600; }
.btn.primary { background: linear-gradient(90deg,var(--accent), #8b5cf6); color:#fff; box-shadow: 0 6px 20px rgba(99,102,241,0.14); }
.btn.outline { background:transparent; border:1px solid rgba(255,255,255,0.06); color:#fff; }

/* --- main layout --- */
.main { display:grid; grid-template-columns: 1fr 320px; gap:var(--gap); margin-top:18px; }
.stage { background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01)); padding:18px; border-radius:12px; box-shadow:var(--shadow); min-height:420px; display:flex; flex-direction:column; gap:12px; }
.sidebar { display:flex; flex-direction:column; gap:14px; }

/* header inside stage */
.grid-header { display:flex; justify-content:space-between; align-items:center; gap:10px; }
.grid-header h2 { margin:0; color:#fff; font-size:18px; }
.grid-header .stats { color:var(--muted); font-size:13px; display:flex; gap:12px; align-items:center; }

/* --- video grid --- */
.video-grid { display:grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap:16px; align-items:start; }
.video-tile { position:relative; background:var(--tile-bg); border-radius:12px; overflow:hidden; min-height:160px; display:flex; align-items:center; justify-content:center; aspect-ratio:16/9; box-shadow: 0 10px 30px rgba(2,6,23,0.6) inset; border: 1px solid rgba(255,255,255,0.03); }
.video-tile video { width:100%; height:100%; object-fit:cover; display:block; background:linear-gradient(180deg,#000,#05060a); }

/* local tile styling */
.local-tile { outline: 2px solid rgba(99,102,241,0.06); }

/* tile footer (name / badges) */
.tile-footer { position:absolute; left:12px; right:12px; bottom:12px; display:flex; justify-content:space-between; align-items:center; gap:8px; background: linear-gradient(180deg, rgba(0,0,0,0.08), rgba(0,0,0,0.18)); padding:8px 10px; border-radius:10px; }
.tile-footer .name { font-weight:700; font-size:13px; color:#fff; }
.tile-footer .meta { font-size:12px; color:var(--muted); }
.badge { display:inline-block; padding:4px 8px; border-radius:999px; background:rgba(255,255,255,0.04); font-size:12px; color:#dbeafe; }
.badge.muted { background: rgba(255,255,255,0.02); color:var(--muted); }

/* --- participants list / sidebar cards --- */
.card { background:linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01)); padding:12px; border-radius:12px; box-shadow:var(--shadow); }
.card.small { padding:10px; font-size:13px; color:var(--muted); }
.participants { list-style:none; padding:0; margin:8px 0 0 0; display:flex; flex-direction:column; gap:8px; }
.participants li { display:flex; gap:10px; align-items:center; padding:8px; border-radius:8px; background:rgba(255,255,255,0.01); }
.participants li .dot { width:10px; height:10px; border-radius:50%; background:var(--accent); }
.tips { margin:0; padding-left:16px; color:var(--muted); }

/* --- log area --- */
.log pre { background: rgba(0,0,0,0.35); color: #dff1ff; padding:10px; border-radius:8px; max-height:140px; overflow:auto; margin:0; font-size:13px; }

/* --- footer --- */
.footer { margin-top:16px; display:flex; justify-content:space-between; color:var(--muted); font-size:13px; align-items:center; }

/* --- responsive --- */
@media (max-width: 980px) {
  .main { grid-template-columns: 1fr; }
  .sidebar { order: 2; }
  .stage { order: 1; }
  .video-grid { grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); }
  .actions { flex-wrap:wrap; gap:8px; }
  .room-input { width:150px; }
}
