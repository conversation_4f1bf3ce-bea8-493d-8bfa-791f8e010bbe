# engagement.py - Real-time engagement tracking and gamification
import time
import logging
from collections import defaultdict
from typing import Dict, List, Any, Optional

log = logging.getLogger("engagement")

# Global engagement data
engagement_data = defaultdict(lambda: {
    "participants": {},
    "leaderboard": [],
    "speaking_stats": {},
    "nudge_history": [],
    "engagement_events": []
})

class EngagementTracker:
    def __init__(self):
        self.attention_weights = {
            "speaking": 0.4,
            "attention_score": 0.3,
            "interaction": 0.2,
            "consistency": 0.1
        }
        
    def calculate_engagement_score(self, participant_data: Dict) -> float:
        """Calculate comprehensive engagement score for a participant"""
        try:
            # Speaking engagement (normalized by meeting duration)
            speaking_time = participant_data.get("speaking_time", 0)
            meeting_duration = time.time() - participant_data.get("joined_at", time.time())
            speaking_ratio = min(1.0, speaking_time / max(meeting_duration, 1)) if meeting_duration > 0 else 0
            
            # Attention score (average of recent scores)
            attention_scores = participant_data.get("attention_scores", [])
            avg_attention = sum(attention_scores[-10:]) / len(attention_scores[-10:]) if attention_scores else 0.5
            
            # Interaction score (based on recent activity)
            last_activity = participant_data.get("last_activity", time.time())
            time_since_activity = time.time() - last_activity
            interaction_score = max(0, 1 - (time_since_activity / 300))  # Decay over 5 minutes
            
            # Consistency score (how consistent their engagement has been)
            if len(attention_scores) > 5:
                consistency_score = 1 - (max(attention_scores[-10:]) - min(attention_scores[-10:])) / 2
            else:
                consistency_score = 0.5
            
            # Weighted final score
            engagement_score = (
                self.attention_weights["speaking"] * speaking_ratio +
                self.attention_weights["attention_score"] * avg_attention +
                self.attention_weights["interaction"] * interaction_score +
                self.attention_weights["consistency"] * consistency_score
            )
            
            return min(1.0, max(0.0, engagement_score))
            
        except Exception as e:
            log.error(f"Error calculating engagement score: {e}")
            return 0.5

tracker = EngagementTracker()

def update_engagement(room: str, participant_id: str, activity_type: str, data: Dict = None) -> Dict:
    """Update engagement metrics for a participant"""
    try:
        if data is None:
            data = {}
            
        room_data = engagement_data[room]
        
        # Initialize participant if not exists
        if participant_id not in room_data["participants"]:
            room_data["participants"][participant_id] = {
                "joined_at": time.time(),
                "last_activity": time.time(),
                "speaking_time": 0,
                "attention_scores": [],
                "interaction_count": 0,
                "engagement_score": 0.5,
                "activities": []
            }
        
        participant = room_data["participants"][participant_id]
        participant["last_activity"] = time.time()
        
        # Update based on activity type
        if activity_type == "speaking":
            words_count = data.get("words_count", 0)
            participant["speaking_time"] += words_count * 0.5  # Estimate speaking time
            participant["interaction_count"] += 1
            
        elif activity_type == "attention":
            score = data.get("score", 0.5)
            participant["attention_scores"].append(score)
            # Keep only last 20 scores
            if len(participant["attention_scores"]) > 20:
                participant["attention_scores"] = participant["attention_scores"][-20:]
                
        elif activity_type == "interaction":
            participant["interaction_count"] += 1
            
        # Record activity
        participant["activities"].append({
            "type": activity_type,
            "timestamp": time.time(),
            "data": data
        })
        
        # Keep only last 50 activities
        if len(participant["activities"]) > 50:
            participant["activities"] = participant["activities"][-50:]
        
        # Recalculate engagement score
        participant["engagement_score"] = tracker.calculate_engagement_score(participant)
        
        # Add to engagement events
        room_data["engagement_events"].append({
            "participant_id": participant_id,
            "activity_type": activity_type,
            "timestamp": time.time(),
            "engagement_score": participant["engagement_score"]
        })
        
        # Keep only last 100 events
        if len(room_data["engagement_events"]) > 100:
            room_data["engagement_events"] = room_data["engagement_events"][-100:]
        
        return participant
        
    except Exception as e:
        log.error(f"Error updating engagement: {e}")
        return {}

def get_room_leaderboard(room: str) -> List[Dict]:
    """Get engagement leaderboard for a room"""
    try:
        room_data = engagement_data[room]
        participants = []
        
        for participant_id, data in room_data["participants"].items():
            participants.append({
                "sid": participant_id,
                "name": data.get("name", f"User {participant_id[:8]}"),
                "engagement_score": data.get("engagement_score", 0.5),
                "speaking_time": data.get("speaking_time", 0),
                "avg_attention": sum(data.get("attention_scores", [0.5])[-10:]) / len(data.get("attention_scores", [0.5])[-10:]),
                "last_activity": data.get("last_activity", time.time()),
                "interaction_count": data.get("interaction_count", 0)
            })
        
        # Sort by engagement score
        participants.sort(key=lambda x: x["engagement_score"], reverse=True)
        
        # Assign titles and badges
        leaderboard = []
        for i, participant in enumerate(participants):
            if i == 0 and len(participants) > 1:
                participant["title"] = "🏆 Meeting Champ"
                participant["badge"] = "champion"
            elif i == len(participants) - 1 and len(participants) > 1:
                participant["title"] = "🤫 Silent Listener"
                participant["badge"] = "silent"
            elif participant["engagement_score"] > 0.8:
                participant["title"] = "⭐ Super Engaged"
                participant["badge"] = "super"
            elif participant["engagement_score"] > 0.6:
                participant["title"] = "👍 Active Participant"
                participant["badge"] = "active"
            else:
                participant["title"] = f"#{i+1} Participant"
                participant["badge"] = "participant"
            
            leaderboard.append(participant)
        
        room_data["leaderboard"] = leaderboard
        return leaderboard
        
    except Exception as e:
        log.error(f"Error getting leaderboard: {e}")
        return []

def get_speaking_distribution(room: str) -> Dict:
    """Get speaking time distribution for room participants"""
    try:
        room_data = engagement_data[room]
        distribution = {}
        total_speaking_time = 0
        
        # Calculate total speaking time
        for participant_id, data in room_data["participants"].items():
            speaking_time = data.get("speaking_time", 0)
            total_speaking_time += speaking_time
        
        # Calculate percentages
        for participant_id, data in room_data["participants"].items():
            speaking_time = data.get("speaking_time", 0)
            percentage = (speaking_time / total_speaking_time * 100) if total_speaking_time > 0 else 0
            
            distribution[participant_id] = {
                "time": speaking_time,
                "percentage": percentage,
                "name": data.get("name", f"User {participant_id[:8]}"),
                "words_estimated": int(speaking_time * 2)  # Rough estimate
            }
        
        return distribution
        
    except Exception as e:
        log.error(f"Error getting speaking distribution: {e}")
        return {}

def should_nudge_participant(room: str, participant_id: str) -> Dict:
    """Determine if a participant should be nudged for engagement"""
    try:
        room_data = engagement_data[room]
        
        if participant_id not in room_data["participants"]:
            return {"should_nudge": False, "reason": "participant_not_found"}
        
        participant = room_data["participants"][participant_id]
        current_time = time.time()
        
        # Check various nudge conditions
        last_activity = participant.get("last_activity", current_time)
        engagement_score = participant.get("engagement_score", 0.5)
        time_since_activity = current_time - last_activity
        
        # Check nudge history to avoid spam
        recent_nudges = [
            event for event in room_data["nudge_history"]
            if event["participant_id"] == participant_id and 
            current_time - event["timestamp"] < 600  # Last 10 minutes
        ]
        
        # Nudge conditions
        conditions = {
            "inactive_too_long": time_since_activity > 300,  # 5 minutes
            "low_engagement": engagement_score < 0.3,
            "no_recent_nudges": len(recent_nudges) == 0,
            "meeting_duration": current_time - participant.get("joined_at", current_time) > 120  # At least 2 minutes in meeting
        }
        
        should_nudge = all(conditions.values())
        
        if should_nudge:
            # Record nudge
            nudge_data = {
                "participant_id": participant_id,
                "timestamp": current_time,
                "reason": "low_engagement_and_inactivity",
                "engagement_score": engagement_score,
                "time_since_activity": time_since_activity
            }
            room_data["nudge_history"].append(nudge_data)
            
            # Keep only last 50 nudges
            if len(room_data["nudge_history"]) > 50:
                room_data["nudge_history"] = room_data["nudge_history"][-50:]
        
        return {
            "should_nudge": should_nudge,
            "conditions": conditions,
            "engagement_score": engagement_score,
            "time_since_activity": time_since_activity,
            "recent_nudges_count": len(recent_nudges)
        }
        
    except Exception as e:
        log.error(f"Error checking nudge conditions: {e}")
        return {"should_nudge": False, "reason": "error"}

def get_engagement_analytics(room: str) -> Dict:
    """Get comprehensive engagement analytics for a room"""
    try:
        room_data = engagement_data[room]
        
        # Calculate overall metrics
        participants = room_data["participants"]
        if not participants:
            return {"error": "No participants found"}
        
        engagement_scores = [p.get("engagement_score", 0.5) for p in participants.values()]
        attention_scores = []
        for p in participants.values():
            attention_scores.extend(p.get("attention_scores", []))
        
        analytics = {
            "total_participants": len(participants),
            "average_engagement": sum(engagement_scores) / len(engagement_scores),
            "average_attention": sum(attention_scores) / len(attention_scores) if attention_scores else 0.5,
            "engagement_trend": calculate_engagement_trend(room_data["engagement_events"]),
            "most_active_participant": max(participants.items(), key=lambda x: x[1].get("interaction_count", 0))[0] if participants else None,
            "speaking_balance": calculate_speaking_balance(room),
            "nudges_sent": len(room_data["nudge_history"]),
            "engagement_distribution": {
                "high": len([s for s in engagement_scores if s > 0.7]),
                "medium": len([s for s in engagement_scores if 0.4 <= s <= 0.7]),
                "low": len([s for s in engagement_scores if s < 0.4])
            }
        }
        
        return analytics
        
    except Exception as e:
        log.error(f"Error getting engagement analytics: {e}")
        return {"error": str(e)}

def calculate_engagement_trend(events: List[Dict]) -> str:
    """Calculate overall engagement trend from recent events"""
    if len(events) < 10:
        return "insufficient_data"
    
    recent_events = events[-20:]
    older_events = events[-40:-20] if len(events) >= 40 else events[:-20]
    
    if not older_events:
        return "insufficient_data"
    
    recent_avg = sum(e["engagement_score"] for e in recent_events) / len(recent_events)
    older_avg = sum(e["engagement_score"] for e in older_events) / len(older_events)
    
    diff = recent_avg - older_avg
    
    if diff > 0.1:
        return "improving"
    elif diff < -0.1:
        return "declining"
    else:
        return "stable"

def calculate_speaking_balance(room: str) -> str:
    """Calculate how balanced the speaking distribution is"""
    distribution = get_speaking_distribution(room)
    
    if not distribution:
        return "no_data"
    
    percentages = [data["percentage"] for data in distribution.values()]
    
    if not percentages:
        return "no_data"
    
    # Calculate coefficient of variation
    mean_percentage = sum(percentages) / len(percentages)
    if mean_percentage == 0:
        return "no_speaking"
    
    variance = sum((p - mean_percentage) ** 2 for p in percentages) / len(percentages)
    cv = (variance ** 0.5) / mean_percentage
    
    if cv < 0.5:
        return "balanced"
    elif cv < 1.0:
        return "somewhat_unbalanced"
    else:
        return "very_unbalanced"

# Cleanup function for old data
def cleanup_old_data(max_age_hours: int = 24):
    """Clean up old engagement data"""
    try:
        current_time = time.time()
        cutoff_time = current_time - (max_age_hours * 3600)
        
        rooms_to_remove = []
        for room, data in engagement_data.items():
            # Check if room has any recent activity
            has_recent_activity = False
            for participant_data in data["participants"].values():
                if participant_data.get("last_activity", 0) > cutoff_time:
                    has_recent_activity = True
                    break
            
            if not has_recent_activity:
                rooms_to_remove.append(room)
        
        for room in rooms_to_remove:
            del engagement_data[room]
            log.info(f"Cleaned up old engagement data for room: {room}")
        
        return len(rooms_to_remove)
        
    except Exception as e:
        log.error(f"Error cleaning up engagement data: {e}")
        return 0
