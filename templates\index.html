<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>AgamAI — Multi-Party Call</title>
  <link rel="stylesheet" href="/static/style.css">
</head>
<body>
  <div class="app">
    <header class="topbar">
      <div class="brand">
        <svg class="logo" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M3 12a9 9 0 1118 0 9 9 0 01-18 0zm9-5a1 1 0 000 2 3 3 0 013 3 1 1 0 002 0 5 5 0 00-5-5z"/></svg>
        <div>
          <div class="title">AgamAI Labs</div>
          <div class="subtitle">Real-time Collaboration — Multi-party (mesh)</div>
        </div>
      </div>

      <div class="actions">
        <input id="roomInput" class="room-input" placeholder="room name" value="testroom" aria-label="Room name" />
        <button id="joinBtn" class="btn primary">Join</button>
        <button id="leaveBtn" class="btn outline" disabled>Leave</button>
      </div>
    </header>

    <main class="main">
      <section class="stage">
        <div class="grid-header">
          <h2>Call Stage</h2>
          <div class="stats">
            <span>RTT: <strong id="rtt">—</strong> ms</span>
            <span>Packet loss: <strong id="pl">—</strong> %</span>
          </div>
        </div>

        <!-- Responsive grid: local + remotes -->
        <div id="remotesGrid" class="video-grid" aria-live="polite">
          <!-- Local tile -->
          <div id="wrap_local" class="video-tile local-tile">
            <video id="localVideo" autoplay muted playsinline></video>
            <div class="tile-footer">
              <div class="name">You</div>
              <div class="meta">Local • <span id="localBadge" class="badge muted">Muted</span></div>
            </div>
          </div>

          <!-- dynamic remote tiles appended here by main.js -->
        </div>

        <div class="log">
          <pre id="debug" aria-hidden="false"></pre>
        </div>
      </section>

      <aside class="sidebar">
        <div class="card">
          <h3>Participants</h3>
          <ul id="participantsList" class="participants">
            <li class="placeholder">No participants yet</li>
          </ul>
        </div>

        <div class="card">
          <h3>Live Transcript</h3>
          <div id="transcriptBox" style="max-height:200px;overflow:auto;font-size:13px;"></div>
        </div>

        <div class="card">
          <h3>Summary & Action Items</h3>
          <button id="summarizeBtn" class="btn outline">Generate Summary</button>
          <div id="summaryBox" style="white-space:pre-wrap;font-size:13px;margin-top:8px;"></div>
        </div>

        <div class="card small">
          <h4>Quick Metrics</h4>
          <div>Network Mode: <strong id="netMode">normal</strong></div>
          <div>Attention: <strong id="attScore">—</strong></div>
        </div>

        <div class="card small">
          <h4>Quick Tips</h4>
          <ul class="tips">
            <li>Use same room name to join peers.</li>
            <li>TURN required for cross-network connectivity.</li>
            <li>Use headset for better audio.</li>
          </ul>
        </div>
      </aside>
    </main>

    <footer class="footer">
      <div>© AgamAI Labs — Hackathon Demo</div>
      <div class="links"><a href="#" target="_blank">Docs</a> · <a href="#" target="_blank">Source</a></div>
    </footer>
  </div>

  <script src="https://cdn.socket.io/4.6.1/socket.io.min.js"></script>
  <script src="/static/main.js" defer></script>
</body>
</html>
